import { Injectable } from "@angular/core";
import {
  HttpInterceptor,
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpErrorResponse,
} from "@angular/common/http";
import { Observable, throwError } from "rxjs";
import { catchError, finalize } from "rxjs/operators";
import { Router } from "@angular/router";
import { AuthService } from "src/app/services/auth/auth.service";
import { LoaderService } from "../services/loader/loader.service";

@Injectable()
export class IdpInterceptor implements HttpInterceptor {
  constructor(
    private router: Router,
    private loaderService: LoaderService,
    private authService: AuthService
  ) {}

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    const skipLoader = req.headers.has("Skip-Loader");
    if (!skipLoader) {
      this.loaderService.show();
    }

    const token = localStorage.getItem("tokenIrrig");
    let modifiedReq = req;

    if (token) {
      modifiedReq = req.clone({
        setHeaders: {
          Authorization: token // ✅ sans "Bearer"
        }
      });
    }

    return next.handle(modifiedReq).pipe(
      catchError((error: HttpErrorResponse) => {
        if (error.status === 401) {
          console.warn("⛔ Token expiré ou invalide. Redirection vers login.");
          this.authService.logout();
        }
        return throwError(() => error);
      }),
      finalize(() => {
        if (!skipLoader) {
          this.loaderService.hide();
        }
      })
    );
  }
}
