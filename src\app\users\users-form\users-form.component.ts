import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ModalController, NavParams, ToastController } from '@ionic/angular';
import { AuthService } from 'src/app/services/auth/auth.service';
import { TranslationService } from 'src/app/services/translate/translation.service';
import { EmailAccountServiceService } from 'src/app/services/users/email-account-service.service';
import { UsersService } from 'src/app/services/users/users.service';

@Component({
  selector: 'app-users-form',
  templateUrl: './users-form.component.html',
  styleUrls: ['./users-form.component.scss'],
})
export class UsersFormComponent  implements OnInit {

 userForm: FormGroup;
  userId: string | null = null;
  availableRoles: { id: string; name: string }[] = [];

  constructor(
    private fb: FormBuilder,
    private userService: UsersService,
    private authService: AuthService,
    private router: Router,
    private translate: TranslationService,
    private emailService: EmailAccountServiceService,
    private toastController: ToastController,
    private modalCtrl: ModalController,
    private navParams: NavParams
  ) {
    this.userForm = this.fb.group({
      username: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      password: [''],
      role: ['', Validators.required],
    });
  }
  
  ngOnInit(): void {
    this.userId = this.navParams.get('userId');

    this.userService.getAllRoles().subscribe((roles) => {
      const allowedRoles = ['technicien', 'admin', 'commercial', 'client'];
      this.availableRoles = roles.filter((role) => allowedRoles.includes(role.name));
    });

    if (this.userId) {
      this.userService.getUserWithRolesById(this.userId).subscribe((user) => {
        const roleName = user.roles?.find((r: string) =>
          ['technicien', 'admin', 'commercial', 'client'].includes(r)
        );

        this.userForm.patchValue({
          username: user.username,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: roleName || '',
        });

        this.userForm.get('password')?.clearValidators();
        this.userForm.get('password')?.updateValueAndValidity();
      });
    } else {
      this.userForm.get('password')?.setValidators([Validators.required]);
    }
  }

  private removeEmptyFields(obj: any): any {
    return Object.fromEntries(Object.entries(obj).filter(([_, v]) => v != null && v !== ''));
  }

  async onSubmit(): Promise<void> {
    if (!this.userForm.valid) {
      this.showToast(this.translate.getTranslation('please_fill_out_the_form_correctly'));
      return;
    }

    const filteredValues = this.removeEmptyFields(this.userForm.value);
    const selectedRole = this.availableRoles.find(role => role.name === filteredValues.role);

    if (!selectedRole) {
      this.showToast(this.translate.getTranslation('please_select_a_valid_role'));
      return;
    }

    const payloadWithRole = {
      ...filteredValues,
      role: {
        id: selectedRole.id,
        name: selectedRole.name,
      },
    };

    const currentUserId = this.authService.getAuthenticatedUserId();
    const isSelfUpdate = this.userId === currentUserId;
    const isRoleChanged = selectedRole.name !== this.authService.getCurrentUserRole();

    const handleTokenRefresh = () => {
      const refreshToken = this.authService.getRefreshToken();
      if (!refreshToken) return;

      this.authService.refreshToken(refreshToken).subscribe({
        next: (res) => {
          this.authService.setToken(res.access_token);
          if (res.refresh_token) {
            localStorage.setItem('refreshToken', res.refresh_token);
          }
          this.showToast(this.translate.getTranslation('role_updated'));
          setTimeout(() => this.router.navigate(['/dashboard']), 1000);
        },
        error: () => {
          this.showToast('Erreur lors du rafraîchissement du token.');
        },
      });
    };

    if (this.userId) {
      this.userService.updateUserWithRole(this.userId, payloadWithRole).subscribe({
        next: () => {
          this.showToast(this.translate.getTranslation('user_updated_successfully'));
          this.modalCtrl.dismiss({ refresh: true });

          if (isSelfUpdate && isRoleChanged) {
            handleTokenRefresh();
          }
        },
        error: () => {
          this.showToast(this.translate.getTranslation('error_updating_user'));
        },
      });
    } else {
      this.userService.createUserWithRole(payloadWithRole).subscribe({
        next: () => {
          const emailPayload = {
            email: this.userForm.get('email')?.value,
            firstName: this.userForm.get('firstName')?.value,
            username: this.userForm.get('username')?.value,
            password: this.userForm.get('password')?.value || '123456',
          };

          this.emailService.sendAccountCreatedEmail(emailPayload).catch((err) => {
            console.warn('Email non envoyé :', err);
          });

          this.showToast(this.translate.getTranslation('user_created_successfully'));
          this.modalCtrl.dismiss({ refresh: true });
        },
        error: () => {
          this.showToast(this.translate.getTranslation('error_creating_user'));
        },
      });
    }
  }

  onCancel(): void {
    this.modalCtrl.dismiss({ refresh: false });
  }

  async showToast(message: string) {
    const toast = await this.toastController.create({
      message,
      duration: 3000,
      position: 'bottom',
    });
    await toast.present();
  }

  getRoleIcon(roleName: string): string {
    const role = roleName.toLowerCase();
    if (role.includes('admin')) return 'shield-checkmark-outline';
    if (role.includes('client')) return 'person-outline';
    if (role.includes('technicien')) return 'build-outline';
    if (role.includes('commercial')) return 'briefcase-outline';
    return 'person-outline';
  }

}
