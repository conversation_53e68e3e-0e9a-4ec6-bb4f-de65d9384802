<ion-header>
  <ion-toolbar color="primary">
    <ion-title>
      <ion-icon name="person-circle-outline" class="title-icon"></ion-icon>
      {{ userId ? 'Modifier utilisateur' : 'Créer un utilisateur' }}
    </ion-title>
    <ion-buttons slot="end">
      <ion-button fill="clear" (click)="onCancel()">
        <ion-icon name="close-outline" slot="icon-only"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="form-content background">
  <div class="form-wrapper">
    <form [formGroup]="userForm" (ngSubmit)="onSubmit()">
      <ion-card class="form-section">
        <ion-card-header>
          <ion-card-title class="section-title">
            <ion-icon name="person-outline" class="section-icon"></ion-icon>
            Informations utilisateur
          </ion-card-title>
        </ion-card-header>

        <ion-card-content>
          <ion-grid>
            <ion-row>
              <ion-col size="12" size-md="6">
                <ion-item lines="none">
                  <ion-icon name="person-circle-outline" slot="start" class="field-icon"></ion-icon>
                  <ion-label position="stacked">Nom d'utilisateur</ion-label>
                  <ion-input formControlName="username" placeholder="Entrez le nom d'utilisateur"></ion-input>
                </ion-item>
              </ion-col>

              <ion-col size="12" size-md="6">
                <ion-item lines="none">
                  <ion-icon name="mail-outline" slot="start" class="field-icon"></ion-icon>
                  <ion-label position="stacked">Email</ion-label>
                  <ion-input type="email" formControlName="email" placeholder="Entrez l'email"></ion-input>
                </ion-item>
              </ion-col>
            </ion-row>

            <ion-row>
              <ion-col size="12" size-md="6">
                <ion-item lines="none">
                  <ion-icon name="person-outline" slot="start" class="field-icon"></ion-icon>
                  <ion-label position="stacked">Prénom</ion-label>
                  <ion-input formControlName="firstName" placeholder="Entrez le prénom"></ion-input>
                </ion-item>
              </ion-col>

              <ion-col size="12" size-md="6">
                <ion-item lines="none">
                  <ion-icon name="people-outline" slot="start" class="field-icon"></ion-icon>
                  <ion-label position="stacked">Nom</ion-label>
                  <ion-input formControlName="lastName" placeholder="Entrez le nom"></ion-input>
                </ion-item>
              </ion-col>
            </ion-row>

            <ion-row *ngIf="!userId">
              <ion-col size="12" size-md="6">
                <ion-item lines="none">
                  <ion-icon name="lock-closed-outline" slot="start" class="field-icon"></ion-icon>
                  <ion-label position="stacked">Mot de passe</ion-label>
                  <ion-input type="password" formControlName="password" placeholder="Mot de passe temporaire"></ion-input>
                </ion-item>
              </ion-col>
            </ion-row>

            <ion-row>
              <ion-col size="12" size-md="6">
                <ion-item lines="none">
                  <ion-icon name="shield-half-outline" slot="start" class="field-icon"></ion-icon>
                  <ion-label position="stacked">Rôle</ion-label>
                  <ion-select formControlName="role" placeholder="Choisir un rôle" interface="popover">
                    <ion-select-option *ngFor="let role of availableRoles" [value]="role.name">
                      {{ role.name }}
                    </ion-select-option>
                  </ion-select>
                </ion-item>
              </ion-col>
            </ion-row>
          </ion-grid>
        </ion-card-content>
      </ion-card>
    </form>
  </div>
</ion-content>

<ion-footer class="form-footer">
  <ion-toolbar>
    <ion-grid>
      <ion-row>
        <ion-col size="6">
          <ion-button expand="block" fill="clear" color="medium" (click)="onCancel()">
            Annuler
          </ion-button>
        </ion-col>
        <ion-col size="6">
          <ion-button expand="block" color="primary" [disabled]="userForm.invalid" (click)="onSubmit()">
            <ion-icon [name]="userId ? 'create-outline' : 'save-outline'" slot="start"></ion-icon>
            {{ userId ? 'Mettre à jour' : 'Créer' }}
          </ion-button>
        </ion-col>
      </ion-row>
    </ion-grid>
  </ion-toolbar>
</ion-footer>
