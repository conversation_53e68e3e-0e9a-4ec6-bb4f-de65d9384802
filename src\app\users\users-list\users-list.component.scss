:host {
  display: block;
  background: #f9fafc;
  min-height: 100vh;
  font-family: 'Roboto', sans-serif;
}

// 🧱 En-tête
.page-header {
  background: linear-gradient(135deg, #74ebd5, #9face6); // doux et clair
  padding: 1.2rem 1rem;
  color: #fff;
  font-weight: 600;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 0 0 12px 12px;

  .header-title {
    display: flex;
    align-items: center;
    ion-icon {
      font-size: 1.4rem;
      margin-right: 0.5rem;
    }
  }
}

// 🔍 Filtres
.filter-section {
  padding: 1rem;
  background-color: #ffffff;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);

  ion-searchbar {
    --background: #f1f3f5;
    --color: #111;
    border-radius: 10px;
    margin-bottom: 0.75rem;
  }

  ion-select {
    width: 100%;
    border-radius: 8px;
    --background: #f1f3f5;
    --padding-start: 10px;
    font-size: 0.9rem;
  }
}

// 👤 Carte utilisateur
.user-card {
  background: #ffffff;
  margin: 0.75rem 1rem;
  border-radius: 14px;
  box-shadow: 0 6px 14px rgba(0,0,0,0.06);
  padding: 1rem;
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.99);
  }

  .user-top {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .info {
      .name {
        font-weight: 600;
        font-size: 1.1rem;
        color: #222;
      }

      .email {
        font-size: 0.85rem;
        color: #555;
      }

      .badges {
        margin-top: 0.5rem;
        display: flex;
        flex-wrap: wrap;
        gap: 6px;

        ion-badge {
          font-size: 0.7rem;
          border-radius: 6px;
          padding: 4px 8px;
        }
      }
    }

    .actions {
      display: flex;
      flex-direction: column;
      gap: 0.4rem;

      ion-button {
        --padding-start: 0;
        --padding-end: 0;
        min-width: 36px;
        height: 36px;
        border-radius: 50%;
        --box-shadow: none;

        &.edit {
          color: #388e3c;
          background: rgba(56, 142, 60, 0.12);
        }

        &.delete {
          color: #e53935;
          background: rgba(229, 57, 53, 0.12);
        }
      }
    }
  }
}

// ⏳ Spinner
.loading-spinner {
  margin-top: 4rem;
  text-align: center;

  ion-spinner {
    width: 40px;
    height: 40px;
    color: #3880ff;
  }

  p {
    margin-top: 1rem;
    color: #888;
    font-size: 0.9rem;
  }
}

// ❌ État vide
.empty-list {
  text-align: center;
  color: #aaa;
  padding: 2rem 1rem;

  ion-icon {
    font-size: 3rem;
    margin-bottom: 0.5rem;
    color: #ccc;
  }

  h3 {
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
    color: #444;
  }

  p {
    font-size: 0.85rem;
  }
}

// ⬇️ Load More
.load-more {
  text-align: center;
  margin: 1.5rem 0;

  ion-button {
    --background: #50c9c3;
    --background-hover: #36b1ac;
    --color: white;
    font-weight: bold;
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
  }
}
