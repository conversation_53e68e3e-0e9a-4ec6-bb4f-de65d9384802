<ion-header>
  <ion-toolbar color="primary">
    <ion-title>
      <ion-icon name="people-outline" class="title-icon"></ion-icon>
      Liste des utilisateurs
    </ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="openUserForm()">
        <ion-icon name="add-outline" slot="icon-only"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding background">

  <!-- Search & Filter -->
  <ion-card class="form-section">
    <ion-card-header>
      <ion-card-title class="section-title">
        <ion-icon name="filter-outline" class="section-icon"></ion-icon>
        Filtres
      </ion-card-title>
    </ion-card-header>

    <ion-card-content>
      <ion-grid>
        <ion-row>
          <ion-col size="12" size-md="6">
            <ion-item lines="none">
              <ion-icon name="search-outline" slot="start" class="field-icon"></ion-icon>
              <ion-input placeholder="Rechercher..." [(ngModel)]="searchTerm" (ionInput)="applyFilters()"></ion-input>
            </ion-item>
          </ion-col>

          <ion-col size="12" size-md="6">
            <ion-item lines="none">
              <ion-icon name="mail-unread-outline" slot="start" class="field-icon"></ion-icon>
              <ion-label>Email vérifié</ion-label>
              <ion-select [(ngModel)]="emailFilter" (ionChange)="applyFilters()" interface="popover">
                <ion-select-option value="all">Tous</ion-select-option>
                <ion-select-option value="true">Oui</ion-select-option>
                <ion-select-option value="false">Non</ion-select-option>
              </ion-select>
            </ion-item>
          </ion-col>
        </ion-row>
      </ion-grid>
    </ion-card-content>
  </ion-card>

  <!-- Loading -->
  <ion-spinner *ngIf="loading" class="spinner-center" color="primary"></ion-spinner>

  <!-- Users List -->
  <ion-card *ngFor="let user of filteredUsers" class="user-card">
    <ion-item lines="none">
      <ion-avatar slot="start">
        <ion-icon name="person-circle-outline" size="large"></ion-icon>
      </ion-avatar>
      <ion-label>
        <h2>{{ user.firstName }} {{ user.lastName }}</h2>
        <p>{{ user.email }}</p>
        <ion-badge [color]="user.emailVerified ? 'success' : 'medium'">
          {{ user.emailVerified ? 'Vérifié' : 'Non vérifié' }}
        </ion-badge>
        <ion-badge *ngFor="let role of user.roles" [color]="getRoleColor(role)">
          {{ role }}
        </ion-badge>
      </ion-label>
      <ion-buttons slot="end">
        <ion-button color="primary" (click)="openUserForm(user.id)">
          <ion-icon name="create-outline"></ion-icon>
        </ion-button>
        <ion-button color="danger" *ngIf="user.id" (click)="deleteUser(user.id)">
          <ion-icon name="trash-outline"></ion-icon>
        </ion-button>
      </ion-buttons>
    </ion-item>
  </ion-card>

  <!-- Empty State -->
  <div *ngIf="!loading && filteredUsers.length === 0" class="ion-text-center">
    <ion-icon name="person-remove-outline" size="large" color="medium"></ion-icon>
    <p>Aucun utilisateur trouvé.</p>
  </div>

</ion-content>
