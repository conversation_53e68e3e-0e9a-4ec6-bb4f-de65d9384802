// === VARIABLES DE COULEURS ===
:host {
  --border-radius: 12px;
  --field-spacing: 16px;
  --border-color: #d1d5db;
  --border-color-focus: #6b7280;
  --background-color: #ffffff;
}

// === TITRE AVEC ICÔNE ===
.title-icon {
  margin-right: 8px;
  color: var(--ion-color-medium);
}

// === CONTENEUR PRINCIPAL ===
.form-wrapper {
  padding: 16px;
  background: #f9fafb;
}

// === SECTIONS DU FORMULAIRE ===
ion-card {
  border-radius: var(--border-radius);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  background: var(--background-color);
  margin-bottom: 16px;
}

ion-card-header {
  background: var(--background-color);
  border-bottom: 1px solid #e5e7eb;

  ion-card-title {
    color: var(--ion-color-dark);
    font-weight: 600;
  }
}

ion-card-content {
  padding: 24px;
}

// === TITRES DE SECTION ===
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  h3 {
    color: var(--ion-color-dark);
    font-weight: 600;
    margin: 0;
  }
}

.section-icon {
  margin-right: 8px;
  font-size: 1.4rem;
  color: var(--ion-color-medium);
}

// === CHAMPS DE FORMULAIRE OUTLINED ===
ion-item {
  --background: var(--background-color);
  --border-color: var(--border-color);
  --border-width: 1px;
  --border-radius: var(--border-radius);
  --padding-start: 16px;
  --padding-end: 16px;
  --min-height: 48px;
  margin-bottom: var(--field-spacing);

  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--background-color);

  transition: border-color 0.2s ease;

  // États focus et hover
  &:hover {
    --border-color: var(--border-color-focus);
  }

  &.item-has-focus {
    --border-color: var(--border-color-focus);
    box-shadow: 0 0 0 3px rgba(107, 114, 128, 0.1);
  }
}

// === LABELS ===
ion-label {
  color: var(--ion-color-medium-shade);
  font-weight: 500;
}

// === INPUTS PERSONNALISÉS ===
ion-input,
ion-select,
ion-textarea {
  color: var(--ion-color-dark);

  &::part(native) {
    padding: 8px 0;
  }
}

// === ICÔNES DE CHAMPS ===
.field-icon {
  font-size: 1.2rem;
  color: var(--ion-color-medium);
  margin-right: 8px;
}

// === BOUTONS STYLISÉS ===
ion-button {
  --border-radius: var(--border-radius);
  margin: 8px 4px;
}

// === MESSAGES D'ERREUR ===
ion-note {
  margin-top: 8px;
  margin-left: 16px;
  font-size: 0.85rem;

  &[color="danger"] {
    color: var(--ion-color-danger);
  }
}

// === RESPONSIVITÉ ===
@media (max-width: 768px) {
  .form-wrapper {
    padding: 12px;
  }

  ion-card-content {
    padding: 16px;
  }
}
