// import { Component, OnInit } from '@angular/core';
// import { ModalController, ToastController } from '@ionic/angular';
// import { IUser } from 'src/app/entities/user.model';
// import { UsersService } from 'src/app/services/users/users.service';
// import { UsersFormComponent } from '../users-form/users-form.component';

// @Component({
//   selector: 'app-users-list',
//   templateUrl: './users-list.component.html',
//   styleUrls: ['./users-list.component.scss'],
// })
// export class UsersListComponent  {
//  users: IUser[] = [];
//   totalUsers = 0;
//   page = 1;
//   limit = 10;
//   searchTerm = '';
//   emailFilter: string = 'all';
//   loading = true;
//   sortField = 'createdTimestamp';
//   sortOrder = 'desc';

//   constructor(
//     private usersService: UsersService,
//     private modalController: ModalController,
//     private toastController: ToastController
//   ) {}

//   ionViewWillEnter() {
//     this.loadPaginatedUsers();
//   }

//   loadPaginatedUsers(): void {
//     this.loading = true;
//     this.usersService
//       .getPaginatedUsersWithRoles(this.page, this.searchTerm, this.sortField, this.sortOrder)
//       .subscribe({
//         next: (res) => {
//           this.users = res.users;
//           this.totalUsers = res.total;
//           this.applyFilters();
//           this.loading = false;
//         },
//         error: () => {
//           this.loading = false;
//           this.showToast('Erreur lors de la récupération des utilisateurs.');
//         },
//       });
//   }

//   applyFilters(): void {
//     let filtered = [...this.users];

//     if (this.emailFilter === 'true') {
//       filtered = filtered.filter(user => user.emailVerified === true);
//     } else if (this.emailFilter === 'false') {
//       filtered = filtered.filter(user => user.emailVerified === false);
//     }

//     if (this.searchTerm.trim()) {
//       const term = this.searchTerm.toLowerCase();
//       filtered = filtered.filter(user =>
//         user.firstName?.toLowerCase().includes(term) ||
//         user.lastName?.toLowerCase().includes(term) ||
//         user.email?.toLowerCase().includes(term) ||
//         user.username?.toLowerCase().includes(term) ||
//         user.roles?.some(role => role.toLowerCase().includes(term))
//       );
//     }

//     this.users = filtered;
//   }

//   async addUser(): Promise<void> {
//     const modal = await this.modalController.create({
//       component: UsersFormComponent,
//       componentProps: { userId: null },
//     });
//     modal.onDidDismiss().then(({ data }) => {
//       if (data?.refresh) this.loadPaginatedUsers();
//     });
//     await modal.present();
//   }

//   async editUser(userId: string): Promise<void> {
//     const modal = await this.modalController.create({
//       component: UsersFormComponent,
//       componentProps: { userId },
//     });
//     modal.onDidDismiss().then(({ data }) => {
//       if (data?.refresh) this.loadPaginatedUsers();
//     });
//     await modal.present();
//   }

//   async deleteUser(userId: string): Promise<void> {
//     const confirmed = confirm('Voulez-vous vraiment supprimer cet utilisateur ?');
//     if (!confirmed) return;

//     this.usersService.deleteUser(userId).subscribe({
//       next: () => {
//         this.showToast('Utilisateur supprimé');
//         this.loadPaginatedUsers();
//       },
//       error: () => {
//         this.showToast('Erreur lors de la suppression');
//       },
//     });
//   }

//   toggleEmailVerification(user: IUser): void {
//     if (!user.id) return;
//     this.usersService.updateUser(user.id, { emailVerified: !user.emailVerified }).subscribe({
//       next: () => {
//         user.emailVerified = !user.emailVerified;
//         this.showToast('Email vérifié mis à jour');
//       },
//       error: () => {
//         this.showToast('Erreur lors de la mise à jour');
//       },
//     });
//   }

//   getRoleBadgeClass(role: string): string {
//     const r = role.toLowerCase();
//     if (r.includes('admin')) return 'role-admin';
//     if (r.includes('client')) return 'role-client';
//     if (r.includes('technicien')) return 'role-technicien';
//     if (r.includes('commercial')) return 'role-commercial';
//     return 'role-default';
//   }

//   async showToast(message: string) {
//     const toast = await this.toastController.create({
//       message,
//       duration: 3000,
//       position: 'bottom',
//     });
//     await toast.present();
//   }

//   loadMore(event: any) {
//     this.page++;
//     this.loadPaginatedUsers();
//     event.target.complete();
//   }

// }
// users-list.component.ts
import { Component } from '@angular/core';
import { ModalController, ToastController } from '@ionic/angular';
import { IUser } from 'src/app/entities/user.model';
import { UsersService } from 'src/app/services/users/users.service';
import { UsersFormComponent } from '../users-form/users-form.component';

@Component({
  selector: 'app-users-list',
  templateUrl: './users-list.component.html',
  styleUrls: ['./users-list.component.scss'],
})
export class UsersListComponent {
  users: IUser[] = [];
  filteredUsers: IUser[] = [];
  totalUsers = 0;
  page = 1;
  limit = 10;
  searchTerm = '';
  emailFilter: string = 'all';
  loading = true;
  sortField = 'createdTimestamp';
  sortOrder = 'desc';

  constructor(
    private usersService: UsersService,
    private modalController: ModalController,
    private toastController: ToastController
  ) {}

  ionViewWillEnter() {
    this.loadPaginatedUsers();
  }

  loadPaginatedUsers(): void {
    this.loading = true;
    this.usersService
      .getPaginatedUsersWithRoles(this.page, this.searchTerm, this.sortField, this.sortOrder)
      .subscribe({
        next: (res) => {
          this.users = res.users;
          this.totalUsers = res.total;
          this.applyFilters();
          this.loading = false;
        },
        error: () => {
          this.loading = false;
          this.showToast('Erreur lors de la récupération des utilisateurs.');
        },
      });
  }

  applyFilters(): void {
    let filtered = [...this.users];

    if (this.emailFilter === 'true') {
      filtered = filtered.filter(user => user.emailVerified === true);
    } else if (this.emailFilter === 'false') {
      filtered = filtered.filter(user => user.emailVerified === false);
    }

    if (this.searchTerm.trim()) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(user =>
        user.firstName?.toLowerCase().includes(term) ||
        user.lastName?.toLowerCase().includes(term) ||
        user.email?.toLowerCase().includes(term) ||
        user.username?.toLowerCase().includes(term) ||
        user.roles?.some(role => role.toLowerCase().includes(term))
      );
    }

    this.filteredUsers = filtered;
  }

  async openUserForm(userId: string | null = null) {
    const modal = await this.modalController.create({
      component: UsersFormComponent,
      componentProps: { userId }
    });

    modal.onDidDismiss().then(({ data }) => {
      if (data?.refresh) this.loadPaginatedUsers();
    });

    await modal.present();
  }

  async deleteUser(userId: string): Promise<void> {
    const confirmed = confirm('Voulez-vous vraiment supprimer cet utilisateur ?');
    if (!confirmed) return;

    this.usersService.deleteUser(userId).subscribe({
      next: () => {
        this.showToast('Utilisateur supprimé');
        this.loadPaginatedUsers();
      },
      error: () => {
        this.showToast('Erreur lors de la suppression');
      },
    });
  }

  toggleEmailVerification(user: IUser): void {
    if (!user.id) return;
    this.usersService.updateUser(user.id, { emailVerified: !user.emailVerified }).subscribe({
      next: () => {
        user.emailVerified = !user.emailVerified;
        this.showToast('Email vérifié mis à jour');
      },
      error: () => {
        this.showToast('Erreur lors de la mise à jour');
      },
    });
  }

  getRoleBadgeClass(role: string): string {
    const r = role.toLowerCase();
    if (r.includes('admin')) return 'role-admin';
    if (r.includes('client')) return 'role-client';
    if (r.includes('technicien')) return 'role-technicien';
    if (r.includes('commercial')) return 'role-commercial';
    return 'role-default';
  }

  async showToast(message: string) {
    const toast = await this.toastController.create({
      message,
      duration: 3000,
      position: 'bottom',
    });
    await toast.present();
  }
getRoleColor(role: string): string {
  const r = role.toLowerCase();
  if (r.includes('admin')) return 'danger';
  if (r.includes('client')) return 'primary';
  if (r.includes('technicien')) return 'tertiary';
  if (r.includes('commercial')) return 'warning';
  return 'medium';
}

  loadMore(event: any) {
    this.page++;
    this.loadPaginatedUsers();
    event.target.complete();
  }
}
